# 微信JS-SDK扫一扫功能集成说明

## 概述

本项目已集成微信JS-SDK的扫一扫功能，可以在微信环境中使用微信原生的扫码功能，在非微信环境中自动回退到uni-app的原生扫码功能。

## 文件结构

```
├── index.html                    # 引入微信JS-SDK
├── pages/scanWater/index.vue     # 扫码页面，集成了微信扫一扫
├── utils/wxJSSDK.js             # 微信JS-SDK工具类
├── utils/wxConfig.js            # 微信配置获取工具
└── docs/微信扫一扫集成说明.md    # 本说明文档
```

## 配置步骤

### 1. 微信公众平台配置

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入 **开发 -> 基本配置**
3. 获取 **AppID** 和 **AppSecret**
4. 配置 **JS接口安全域名**（在 **设置 -> 公众号设置 -> 功能设置** 中）

### 2. 后端API实现（推荐方式）

为了安全性，建议在后端实现获取微信JS-SDK配置的API：

```javascript
// 后端API示例 (Node.js/Express)
app.get('/api/wx/jsapi-config', async (req, res) => {
  try {
    const url = req.query.url
    
    // 1. 获取access_token（建议缓存，有效期7200秒）
    const accessToken = await getAccessToken()
    
    // 2. 获取jsapi_ticket（建议缓存，有效期7200秒）
    const jsapiTicket = await getJsapiTicket(accessToken)
    
    // 3. 生成签名
    const nonceStr = generateNonceStr()
    const timestamp = Math.floor(Date.now() / 1000)
    const signature = generateSignature({
      jsapi_ticket: jsapiTicket,
      noncestr: nonceStr,
      timestamp,
      url
    })
    
    res.json({
      code: 0,
      data: {
        appId: 'your_app_id',
        timestamp,
        nonceStr,
        signature
      }
    })
  } catch (error) {
    res.json({
      code: -1,
      message: error.message
    })
  }
})
```

### 3. 前端配置

#### 方式1：使用后端API（推荐）

修改 `pages/scanWater/index.vue` 中的配置：

```javascript
// 取消注释这行，使用后端API获取配置
const config = await getWxConfigFromAPI(getCurrentUrl())

// 注释掉手动配置
// const config = {
//   appId: 'your_app_id',
//   jsapi_ticket: 'your_jsapi_ticket',
// }
```

#### 方式2：手动配置（仅用于测试）

修改 `utils/wxConfig.js` 中的配置：

```javascript
export const WX_CONFIG = {
  APP_ID: 'your_real_app_id',        // 替换为真实的AppID
  APP_SECRET: 'your_real_app_secret' // 替换为真实的AppSecret
}
```

然后修改 `pages/scanWater/index.vue`：

```javascript
// 使用手动配置
const config = await getWxJSSDKConfig()
```

## 使用方法

### 基本使用

扫码功能已经集成在 `pages/scanWater/index.vue` 中，点击扫码按钮即可使用：

- **微信环境**：使用微信原生扫一扫功能
- **非微信环境**：自动回退到uni-app原生扫码功能

### 扫码结果处理

扫码成功后，结果会传递给 `handleScanResult` 函数：

```javascript
const handleScanResult = (result) => {
  uni.showModal({
    title: '扫码成功',
    content: `扫码结果：${result}`,
    success: function (res) {
      if (res.confirm) {
        // 处理扫码结果
        console.log('扫码结果:', result)
        // 在这里添加您的业务逻辑
      }
    }
  })
}
```

## 调试说明

### 开启调试模式

在 `utils/wxJSSDK.js` 中设置：

```javascript
return {
  debug: true, // 开启调试模式
  // ... 其他配置
}
```

### 常见问题

1. **签名错误**
   - 检查 `jsapi_ticket` 是否正确
   - 检查 `url` 参数是否包含 `#` 号（应该去除）
   - 检查时间戳是否为秒级时间戳

2. **接口调用失败**
   - 检查 `jsApiList` 是否包含 `scanQRCode`
   - 检查域名是否在微信公众平台配置的安全域名中

3. **不在微信环境**
   - 系统会自动回退到uni-app原生扫码功能

## 安全注意事项

1. **不要在前端暴露 AppSecret**
2. **access_token 和 jsapi_ticket 应该在后端缓存**
3. **签名算法应该在后端实现**
4. **定期更新 access_token 和 jsapi_ticket**

## 参考文档

- [微信JS-SDK说明文档](https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html)
- [JS-SDK使用权限签名算法](https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html#%E9%99%84%E5%BD%95-JS-SDK%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%E7%AD%BE%E5%90%8D%E7%AE%97%E6%B3%95)

## 测试步骤

1. 确保在微信环境中打开页面
2. 检查浏览器控制台是否有错误信息
3. 点击扫码按钮测试功能
4. 验证扫码结果是否正确处理

## 更新日志

- 2025-07-28: 初始版本，集成微信JS-SDK扫一扫功能
