/**
 * 微信JS-SDK工具类
 * 用于处理微信JS-SDK的配置和签名算法
 * 参考文档：https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html
 */

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
export function generateNonceStr(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成时间戳
 * @returns {number} 当前时间戳（秒）
 */
export function generateTimestamp() {
  return Math.floor(Date.now() / 1000)
}

/**
 * 对象转URL参数字符串
 * @param {Object} obj 参数对象
 * @returns {string} URL参数字符串
 */
function objToUrlParams(obj) {
  const params = Object.keys(obj).sort().map(key => {
    return `${key}=${obj[key]}`
  }).join('&')
  return params
}

/**
 * SHA1加密（简单实现，生产环境建议使用专业的加密库）
 * @param {string} str 待加密字符串
 * @returns {string} SHA1加密结果
 */
function sha1(str) {
  // 注意：这里需要引入SHA1加密库，比如crypto-js
  // 由于是前端环境，这里提供一个简化的示例
  // 实际使用时建议使用专业的加密库
  console.warn('SHA1加密需要引入专业的加密库，如crypto-js')
  return str // 这里只是占位，实际需要真正的SHA1加密
}

/**
 * 生成微信JS-SDK签名
 * 根据微信官方文档的签名算法实现
 * @param {Object} params 签名参数
 * @param {string} params.jsapi_ticket JS接口调用凭证
 * @param {string} params.noncestr 随机字符串
 * @param {number} params.timestamp 时间戳
 * @param {string} params.url 当前网页的URL，不包含#及其后面部分
 * @returns {string} 签名字符串
 */
export function generateSignature({ jsapi_ticket, noncestr, timestamp, url }) {
  // 参与签名的字段包括noncestr（随机字符串）, 有效的jsapi_ticket, timestamp（时间戳）, url（当前网页的URL，不包含#及其后面部分）
  const params = {
    jsapi_ticket,
    noncestr,
    timestamp,
    url
  }
  
  // 对所有待签名参数按照字段名的ASCII 码从小到大排序（字典序）后，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串
  const string1 = objToUrlParams(params)
  
  // 对string1作sha1加密，字段名和字段值都采用原始值，不进行URL 转义
  const signature = sha1(string1)
  
  console.log('签名字符串：', string1)
  console.log('生成的签名：', signature)
  
  return signature
}

/**
 * 获取当前页面URL（不包含#及其后面部分）
 * @returns {string} 当前页面URL
 */
export function getCurrentUrl() {
  return window.location.href.split('#')[0]
}

/**
 * 检查是否在微信环境中
 * @returns {boolean} 是否在微信环境
 */
export function isWeixinBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 微信JS-SDK配置对象生成器
 * @param {Object} config 配置参数
 * @param {string} config.appId 公众号的唯一标识
 * @param {string} config.jsapi_ticket JS接口调用凭证
 * @param {string} config.url 当前网页的URL（可选，默认使用当前页面URL）
 * @returns {Object} 微信JS-SDK配置对象
 */
export function createWxConfig({ appId, jsapi_ticket, url }) {
  const nonceStr = generateNonceStr()
  const timestamp = generateTimestamp()
  const currentUrl = url || getCurrentUrl()
  
  const signature = generateSignature({
    jsapi_ticket,
    noncestr: nonceStr,
    timestamp,
    url: currentUrl
  })
  
  return {
    debug: false, // 生产环境建议设为false
    appId,
    timestamp,
    nonceStr,
    signature,
    jsApiList: [
      'scanQRCode', // 扫一扫接口
      'chooseImage', // 拍照或从手机相册中选图接口
      'previewImage', // 预览图片接口
      'uploadImage', // 上传图片接口
      'downloadImage' // 下载图片接口
    ]
  }
}

/**
 * 初始化微信JS-SDK
 * @param {Object} config 配置参数
 * @param {Function} onReady 初始化成功回调
 * @param {Function} onError 初始化失败回调
 */
export function initWxJSSDK(config, onReady, onError) {
  if (!isWeixinBrowser()) {
    console.log('不在微信环境中')
    onError && onError({ message: '不在微信环境中' })
    return
  }
  
  if (typeof wx === 'undefined') {
    console.error('微信JS-SDK未加载')
    onError && onError({ message: '微信JS-SDK未加载' })
    return
  }
  
  const wxConfig = createWxConfig(config)
  
  wx.config(wxConfig)
  
  wx.ready(function() {
    console.log('微信JS-SDK初始化成功')
    onReady && onReady()
  })
  
  wx.error(function(res) {
    console.error('微信JS-SDK初始化失败：', res)
    onError && onError(res)
  })
}
