<template>
	<view class="content">
		<view class="function-buttons">
			<!-- 扫码用水 -->
			<scanWater></scanWater>
			<!-- 蓝牙用水 -->
			<bluetoothWater></bluetoothWater>
			<!-- 使用密码设备 -->
			<view class="function-btn" @click="openPasswordConfig">
				<text class="iconfont">&#xe62c;</text>
				<text class="btn-text">密码设备</text>
			</view>
			<!-- 故障报修功能 -->
			<view class="function-btn" @click="openRepairReport">
				<text class="iconfont">&#xe601;</text>
				<text class="btn-text">故障报修</text>
			</view>
			<!-- 用户信息 -->
			<view class="function-btn" @click="openUserInfo">
				<text class="iconfont">&#xe62d;</text>
				<text class="btn-text">用户信息</text>
			</view>
			<!-- 交易记录 -->
			<view class="function-btn" @click="openTransactionRecord">
				<text class="iconfont">&#xe637;</text>
				<text class="btn-text">交易记录</text>
			</view>
			<!-- 微信JS-SDK测试 -->
			<view class="function-btn test-btn" @click="openWxTest">
				<text class="iconfont">&#xe600;</text>
				<text class="btn-text">微信测试</text>
			</view>
		</view>
	</view>
</template>

<script setup>

import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import scanWater from '../scanWater/index.vue'//扫码用水
import bluetoothWater from '../bluetoothWater/index.vue'//蓝牙用水

// 响应式数据
const title = ref('Hello')

// 打开密码配置
const openPasswordConfig = () => {
	uni.navigateTo({
		url: '/pages/passwordDevice/index'
	})
}

// 打开故障报修
const openRepairReport = () => {
	uni.navigateTo({
		url: '/pages/repairReport/index'
	})
}

// 打开用户信息
const openUserInfo = () => {
	uni.navigateTo({
		url: '/pages/userInfo/index'
	})
}

// 打开交易记录
const openTransactionRecord = () => {
	uni.navigateTo({
		url: '/pages/transactionRecord/index'
	})
}

// 打开微信JS-SDK测试页面
const openWxTest = () => {
	uni.navigateTo({
		url: '/pages/wxTest/wxSDKTest'
	})
}

// 生命周期钩子
onLoad(() => {
	// 原来的 onLoad 逻辑可以放在这里
})


</script>

<style lang="scss" scoped>
/* 引入 iconfont 字体图标 */
@import url('//at.alicdn.com/t/c/font_4701566_abc123.css');

.content {
	padding: 40rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.function-buttons {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 20rpx;
	// margin-top: 100rpx;
	justify-content: space-between;
}

.function-btn {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	width: calc(50% - 60rpx);
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	min-height: 120rpx;
	margin-bottom: 20rpx;
}

.function-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
}

/* 蓝牙用水 - 粉紫色 */
.function-btn:nth-child(2) {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.function-btn:nth-child(2):active {
	box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
}

/* 密码设备 - 蓝色 */
.function-btn:nth-child(3) {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	box-shadow: 0 8rpx 20rpx rgba(79, 172, 254, 0.3);
}

.function-btn:nth-child(3):active {
	box-shadow: 0 4rpx 10rpx rgba(79, 172, 254, 0.3);
}

/* 故障报修 - 橙红色 */
.function-btn:nth-child(4) {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
	box-shadow: 0 8rpx 20rpx rgba(250, 112, 154, 0.3);
}

.function-btn:nth-child(4):active {
	box-shadow: 0 4rpx 10rpx rgba(250, 112, 154, 0.3);
}

/* 用户信息 - 薄荷绿 */
.function-btn:nth-child(5) {
	background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
	box-shadow: 0 8rpx 20rpx rgba(86, 171, 47, 0.3);
}

.function-btn:nth-child(5):active {
	box-shadow: 0 4rpx 10rpx rgba(86, 171, 47, 0.3);
}

/* 交易记录 - 紫色 */
.function-btn:nth-child(6) {
	background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
	box-shadow: 0 8rpx 20rpx rgba(131, 96, 195, 0.3);
}

.function-btn:nth-child(6):active {
	box-shadow: 0 4rpx 10rpx rgba(131, 96, 195, 0.3);
}

/* 微信测试 - 金色 */
.test-btn {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
	box-shadow: 0 8rpx 20rpx rgba(240, 147, 251, 0.3) !important;
}

.test-btn:active {
	box-shadow: 0 4rpx 10rpx rgba(240, 147, 251, 0.3) !important;
}

.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}

.btn-text {
	font-size: 30rpx;
	color: #ffffff;
	font-weight: 500;
}
</style>
