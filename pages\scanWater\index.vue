<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater">
		<text class="iconfont">&#xe600;</text>
		<text class="btn-text">扫码用水</text>
	</view>
</template>

<script setup>

import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { initWxJSSDK, isWeixinBrowser, getCurrentUrl } from '../../utils/wxJSSDK.js'
import { getWxConfigFromAPI } from '../../utils/wxConfig.js'

// 微信JS-SDK配置状态
const isWxReady = ref(false)

// 生命周期钩子
onLoad(() => {
	// 初始化微信JS-SDK
	initWeixinJSSDK()
})

// 初始化微信JS-SDK
const initWeixinJSSDK = async () => {
	// 检查是否在微信环境中
	if (!isWeixinBrowser()) {
		console.log('不在微信环境中，使用uni-app原生扫码')
		return
	}

	try {
		// 方式1：从后端API获取配置（推荐）
		// const config = await getWxConfigFromAPI(getCurrentUrl())

		// 方式2：手动配置（用于测试，需要您提供真实的配置信息）
		const config = {
			appId: 'your_app_id', // 替换为您的公众号AppID
			jsapi_ticket: 'your_jsapi_ticket', // 替换为您的jsapi_ticket
			// url 参数可选，默认使用当前页面URL
		}

		// 使用工具类初始化微信JS-SDK
		initWxJSSDK(
			config,
			() => {
				// 初始化成功回调
				console.log('微信JS-SDK初始化成功')
				isWxReady.value = true
			},
			(error) => {
				// 初始化失败回调
				console.log('微信JS-SDK初始化失败：', error)
				isWxReady.value = false
			}
		)
	} catch (error) {
		console.error('获取微信配置失败：', error)
		isWxReady.value = false
	}
}

// 开始扫码
const scanWater = () => {
	console.log('开始扫码')

	// 检查是否在微信环境中且JS-SDK已准备好
	if (typeof wx !== 'undefined' && isWxReady.value) {
		// 使用微信JS-SDK扫一扫
		wx.scanQRCode({
			needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果
			scanType: ["qrCode","barCode"], // 可以指定扫二维码还是一维码，默认二者都有
			success: function (res) {
				console.log('微信扫码结果：' + res.resultStr);
				handleScanResult(res.resultStr);
			},
			fail: function (err) {
				console.log("微信扫码失败：", err);
				// 微信扫码失败时，回退到uni-app原生扫码
				fallbackToUniScan();
			}
		});
	} else {
		// 不在微信环境中或JS-SDK未准备好，使用uni-app原生扫码
		fallbackToUniScan();
	}
}

// 回退到uni-app原生扫码
const fallbackToUniScan = () => {
	uni.scanCode({
		success: async function (res) {
			console.log('uni-app扫码结果：' + res.result);
			handleScanResult(res.result);
		},
		fail: function (err) {
			console.log("uni-app扫码失败：", err);
		}
	});
}

// 处理扫码结果
const handleScanResult = (result) => {
	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${result}`,
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，可以进行下一步操作
				console.log('用户确认扫码结果')
			}
		}
	})
}

</script>
<style lang="scss" scoped>
.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}
</style>
