# 微信JS-SDK扫一扫功能快速测试指南

## 测试页面

我已经为您创建了一个专门的测试页面：`pages/wxTest/wxSDKTest.vue`

## 测试步骤

### 1. 准备工作

1. **获取微信公众号信息**
   - 登录 [微信公众平台](https://mp.weixin.qq.com/)
   - 进入 **开发 -> 基本配置**
   - 记录下 **AppID** 和 **AppSecret**

2. **配置JS接口安全域名**
   - 在微信公众平台进入 **设置 -> 公众号设置 -> 功能设置**
   - 在 **JS接口安全域名** 中添加您的域名（不需要协议头）
   - 例如：`your-domain.com`

### 2. 访问测试页面

在浏览器中访问测试页面：
```
http://your-domain.com/#/pages/wxTest/wxSDKTest
```

### 3. 使用测试页面

1. **填写配置信息**
   - 在页面顶部输入您的 **AppID** 和 **AppSecret**
   - 点击 **"获取微信配置"** 按钮

2. **查看状态信息**
   - 检查 **微信环境** 是否显示 "是"
   - 检查 **JS-SDK状态** 是否显示 "已就绪"
   - 查看 **当前URL** 是否正确

3. **查看生成的配置**
   - 页面会显示自动生成的签名配置信息
   - 包括 AppID、Timestamp、NonceStr、Signature

4. **测试扫一扫功能**
   - 点击 **"测试扫一扫"** 按钮
   - 使用微信扫一扫功能扫描二维码或条形码

5. **查看日志信息**
   - 页面底部会显示详细的日志信息
   - 包括每个步骤的执行结果和错误信息

### 4. 常见问题排查

#### 问题1：获取access_token失败
- **可能原因**：AppID或AppSecret错误
- **解决方案**：检查微信公众平台的基本配置信息

#### 问题2：获取jsapi_ticket失败
- **可能原因**：access_token无效或过期
- **解决方案**：重新获取access_token

#### 问题3：微信JS-SDK初始化失败
- **可能原因**：
  - 域名未在微信公众平台配置
  - 签名算法错误
  - URL参数不正确
- **解决方案**：
  - 检查JS接口安全域名配置
  - 查看浏览器控制台的详细错误信息

#### 问题4：扫一扫功能无法使用
- **可能原因**：
  - 不在微信环境中
  - JS-SDK未正确初始化
  - jsApiList配置错误
- **解决方案**：
  - 确保在微信中打开页面
  - 检查JS-SDK初始化状态

### 5. 调试技巧

1. **开启调试模式**
   - 测试页面默认开启了debug模式
   - 微信会弹出详细的调试信息

2. **查看控制台日志**
   - 打开浏览器开发者工具
   - 查看Console标签页的日志信息

3. **检查网络请求**
   - 在Network标签页查看API请求
   - 确认access_token和jsapi_ticket的获取情况

### 6. 生产环境部署

测试成功后，您可以：

1. **集成到扫码页面**
   - 将测试页面的配置逻辑集成到 `pages/scanWater/index.vue`
   - 替换测试用的AppID和AppSecret

2. **实现后端API**
   - 为了安全性，建议在后端实现获取配置的API
   - 前端通过API获取签名配置，而不是直接暴露AppSecret

3. **缓存优化**
   - access_token有效期7200秒，建议缓存
   - jsapi_ticket有效期7200秒，建议缓存

## 示例配置

根据您提供的文档，以下是一个完整的配置示例：

```javascript
// 1. 获取access_token
const tokenUrl = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=YOUR_APPID&secret=YOUR_APPSECRET'

// 2. 获取jsapi_ticket
const ticketUrl = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi'

// 3. 生成签名
const signature = sha1(`jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`)

// 4. 配置微信JS-SDK
wx.config({
  debug: false,
  appId: 'YOUR_APPID',
  timestamp: timestamp,
  nonceStr: nonceStr,
  signature: signature,
  jsApiList: ['scanQRCode']
})
```

## 技术支持

如果在测试过程中遇到问题，请：

1. 查看测试页面的日志信息
2. 检查浏览器控制台的错误信息
3. 参考微信官方文档：https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html

## 更新记录

- 2025-07-28: 创建测试页面和快速测试指南
- 集成完整的签名算法和配置流程
- 添加详细的错误处理和日志记录
