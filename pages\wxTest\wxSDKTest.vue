<template>
  <view class="container">
    <view class="header">
      <text class="title">微信JS-SDK测试页面</text>
    </view>
    
    <view class="config-section">
      <text class="section-title">配置信息</text>
      <view class="input-group">
        <text class="label">AppID:</text>
        <input v-model="config.appId" placeholder="请输入微信公众号AppID" class="input" />
      </view>
      <view class="input-group">
        <text class="label">AppSecret:</text>
        <input v-model="config.appSecret" placeholder="请输入微信公众号AppSecret" class="input" type="password" />
      </view>
      <button @click="getWxConfig" class="btn primary" :disabled="loading">
        {{ loading ? '获取中...' : '获取微信配置' }}
      </button>
    </view>
    
    <view class="status-section">
      <text class="section-title">状态信息</text>
      <view class="status-item">
        <text class="status-label">微信环境:</text>
        <text :class="['status-value', isWeixinEnv ? 'success' : 'error']">
          {{ isWeixinEnv ? '是' : '否' }}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">JS-SDK状态:</text>
        <text :class="['status-value', isWxReady ? 'success' : 'error']">
          {{ isWxReady ? '已就绪' : '未就绪' }}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">当前URL:</text>
        <text class="status-value small">{{ currentUrl }}</text>
      </view>
    </view>
    
    <view class="config-display" v-if="wxConfigData">
      <text class="section-title">生成的配置</text>
      <view class="config-item">
        <text class="config-label">AppID:</text>
        <text class="config-value">{{ wxConfigData.appId }}</text>
      </view>
      <view class="config-item">
        <text class="config-label">Timestamp:</text>
        <text class="config-value">{{ wxConfigData.timestamp }}</text>
      </view>
      <view class="config-item">
        <text class="config-label">NonceStr:</text>
        <text class="config-value">{{ wxConfigData.nonceStr }}</text>
      </view>
      <view class="config-item">
        <text class="config-label">Signature:</text>
        <text class="config-value small">{{ wxConfigData.signature }}</text>
      </view>
    </view>
    
    <view class="action-section">
      <button @click="testScan" class="btn success" :disabled="!isWxReady">
        测试扫一扫
      </button>
      <button @click="clearLogs" class="btn secondary">
        清空日志
      </button>
    </view>
    
    <view class="log-section">
      <text class="section-title">日志信息</text>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { 
  isWeixinBrowser, 
  getCurrentUrl, 
  generateNonceStr, 
  generateTimestamp, 
  generateSignature,
  createWxConfig 
} from '../../utils/wxJSSDK.js'

// 响应式数据
const config = ref({
  appId: '',
  appSecret: ''
})

const isWeixinEnv = ref(false)
const isWxReady = ref(false)
const loading = ref(false)
const currentUrl = ref('')
const wxConfigData = ref(null)
const logs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  logs.value.unshift({
    time,
    message,
    type
  })
  console.log(`[${type.toUpperCase()}] ${message}`)
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 获取微信配置
const getWxConfig = async () => {
  if (!config.value.appId || !config.value.appSecret) {
    addLog('请填写AppID和AppSecret', 'error')
    return
  }
  
  loading.value = true
  addLog('开始获取微信配置...')
  
  try {
    // 1. 获取access_token
    addLog('正在获取access_token...')
    const accessTokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${config.value.appId}&secret=${config.value.appSecret}`
    
    const tokenResponse = await fetch(accessTokenUrl)
    const tokenData = await tokenResponse.json()
    
    if (!tokenData.access_token) {
      throw new Error(`获取access_token失败: ${tokenData.errmsg} (${tokenData.errcode})`)
    }
    
    addLog(`获取access_token成功，有效期: ${tokenData.expires_in}秒`, 'success')
    
    // 2. 获取jsapi_ticket
    addLog('正在获取jsapi_ticket...')
    const ticketUrl = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${tokenData.access_token}&type=jsapi`
    
    const ticketResponse = await fetch(ticketUrl)
    const ticketData = await ticketResponse.json()
    
    if (ticketData.errcode !== 0 || !ticketData.ticket) {
      throw new Error(`获取jsapi_ticket失败: ${ticketData.errmsg} (${ticketData.errcode})`)
    }
    
    addLog(`获取jsapi_ticket成功，有效期: ${ticketData.expires_in}秒`, 'success')
    
    // 3. 生成签名配置
    addLog('正在生成签名配置...')
    const wxConfig = createWxConfig({
      appId: config.value.appId,
      jsapi_ticket: ticketData.ticket,
      url: currentUrl.value
    })
    
    wxConfigData.value = wxConfig
    addLog('签名配置生成成功', 'success')
    
    // 4. 初始化微信JS-SDK
    if (typeof wx !== 'undefined') {
      addLog('正在初始化微信JS-SDK...')
      
      wx.config({
        debug: true,
        appId: wxConfig.appId,
        timestamp: wxConfig.timestamp,
        nonceStr: wxConfig.nonceStr,
        signature: wxConfig.signature,
        jsApiList: ['scanQRCode']
      })
      
      wx.ready(() => {
        isWxReady.value = true
        addLog('微信JS-SDK初始化成功！', 'success')
      })
      
      wx.error((res) => {
        addLog(`微信JS-SDK初始化失败: ${JSON.stringify(res)}`, 'error')
      })
    } else {
      addLog('微信JS-SDK未加载', 'error')
    }
    
  } catch (error) {
    addLog(`配置失败: ${error.message}`, 'error')
  } finally {
    loading.value = false
  }
}

// 测试扫一扫
const testScan = () => {
  if (typeof wx === 'undefined') {
    addLog('微信JS-SDK未加载', 'error')
    return
  }
  
  addLog('开始测试扫一扫...')
  
  wx.scanQRCode({
    needResult: 1,
    scanType: ["qrCode", "barCode"],
    success: (res) => {
      addLog(`扫码成功: ${res.resultStr}`, 'success')
      uni.showModal({
        title: '扫码成功',
        content: res.resultStr
      })
    },
    fail: (err) => {
      addLog(`扫码失败: ${JSON.stringify(err)}`, 'error')
    }
  })
}

// 生命周期
onLoad(() => {
  isWeixinEnv.value = isWeixinBrowser()
  currentUrl.value = getCurrentUrl()
  
  addLog(`页面加载完成`)
  addLog(`微信环境: ${isWeixinEnv.value ? '是' : '否'}`)
  addLog(`当前URL: ${currentUrl.value}`)
  
  if (!isWeixinEnv.value) {
    addLog('当前不在微信环境中，某些功能可能无法正常使用', 'warning')
  }
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.config-section, .status-section, .config-display, .action-section, .log-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.input-group {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  margin-bottom: 20rpx;
}

.btn.primary {
  background-color: #007aff;
  color: white;
}

.btn.success {
  background-color: #4cd964;
  color: white;
}

.btn.secondary {
  background-color: #8e8e93;
  color: white;
}

.btn:disabled {
  opacity: 0.5;
}

.status-item, .config-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.status-label, .config-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}

.status-value, .config-value {
  font-size: 28rpx;
  flex: 1;
  word-break: break-all;
}

.status-value.success {
  color: #4cd964;
}

.status-value.error {
  color: #ff3b30;
}

.small {
  font-size: 24rpx !important;
}

.log-container {
  height: 400rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 15rpx;
  padding: 10rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.log-item.info {
  background-color: #f0f9ff;
  border-left: 4rpx solid #007aff;
}

.log-item.success {
  background-color: #f0fff4;
  border-left: 4rpx solid #4cd964;
}

.log-item.error {
  background-color: #fff5f5;
  border-left: 4rpx solid #ff3b30;
}

.log-item.warning {
  background-color: #fffbf0;
  border-left: 4rpx solid #ff9500;
}

.log-time {
  color: #999;
  margin-right: 20rpx;
}

.log-content {
  color: #333;
}
</style>
