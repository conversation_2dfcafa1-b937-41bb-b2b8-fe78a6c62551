/**
 * 微信JS-SDK配置文件
 * 
 * 根据微信官方文档获取配置信息：
 * https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html#%E9%99%84%E5%BD%95-JS-SDK%E4%BD%BF%E7%94%A8%E6%9D%83%E9%99%90%E7%AD%BE%E5%90%8D%E7%AE%97%E6%B3%95
 */

/**
 * 微信公众号配置
 * 需要在微信公众平台获取
 */
export const WX_CONFIG = {
  // 公众号的AppID，在微信公众平台 -> 开发 -> 基本配置中获取
  APP_ID: 'your_app_id_here',
  
  // 公众号的AppSecret，在微信公众平台 -> 开发 -> 基本配置中获取
  // 注意：AppSecret不应该暴露在前端代码中，应该在后端使用
  APP_SECRET: 'your_app_secret_here'
}

/**
 * 获取Access Token的API地址
 * 文档：https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html#%E8%8E%B7%E5%8F%96access_token
 */
export const ACCESS_TOKEN_URL = 'https://api.weixin.qq.com/cgi-bin/token'

/**
 * 获取jsapi_ticket的API地址
 * 文档：https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html#%E8%8E%B7%E5%8F%96jsapi_ticket
 */
export const JSAPI_TICKET_URL = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket'

/**
 * 获取Access Token
 * 注意：这个函数应该在后端调用，这里仅作为示例
 * @returns {Promise<string>} Access Token
 */
export async function getAccessToken() {
  try {
    const response = await fetch(`${ACCESS_TOKEN_URL}?grant_type=client_credential&appid=${WX_CONFIG.APP_ID}&secret=${WX_CONFIG.APP_SECRET}`)
    const data = await response.json()
    
    if (data.access_token) {
      console.log('获取Access Token成功')
      return data.access_token
    } else {
      throw new Error(`获取Access Token失败: ${data.errmsg}`)
    }
  } catch (error) {
    console.error('获取Access Token出错:', error)
    throw error
  }
}

/**
 * 获取jsapi_ticket
 * 注意：这个函数应该在后端调用，这里仅作为示例
 * @param {string} accessToken Access Token
 * @returns {Promise<string>} jsapi_ticket
 */
export async function getJsapiTicket(accessToken) {
  try {
    const response = await fetch(`${JSAPI_TICKET_URL}?access_token=${accessToken}&type=jsapi`)
    const data = await response.json()
    
    if (data.ticket) {
      console.log('获取jsapi_ticket成功')
      return data.ticket
    } else {
      throw new Error(`获取jsapi_ticket失败: ${data.errmsg}`)
    }
  } catch (error) {
    console.error('获取jsapi_ticket出错:', error)
    throw error
  }
}

/**
 * 获取微信JS-SDK配置所需的参数
 * 注意：在生产环境中，这些操作应该在后端完成，前端通过API获取最终的配置参数
 * @returns {Promise<Object>} 配置参数
 */
export async function getWxJSSDKConfig() {
  try {
    // 步骤1：获取Access Token
    const accessToken = await getAccessToken()
    
    // 步骤2：获取jsapi_ticket
    const jsapiTicket = await getJsapiTicket(accessToken)
    
    return {
      appId: WX_CONFIG.APP_ID,
      jsapi_ticket: jsapiTicket
    }
  } catch (error) {
    console.error('获取微信JS-SDK配置失败:', error)
    throw error
  }
}

/**
 * 推荐的后端API接口设计
 * 
 * 为了安全性，建议在后端实现以下API接口：
 * 
 * GET /api/wx/jsapi-config?url=当前页面URL
 * 
 * 返回格式：
 * {
 *   "code": 0,
 *   "data": {
 *     "appId": "wx1234567890",
 *     "timestamp": 1234567890,
 *     "nonceStr": "randomstring",
 *     "signature": "generated_signature"
 *   }
 * }
 * 
 * 后端实现步骤：
 * 1. 获取并缓存access_token（有效期7200秒）
 * 2. 使用access_token获取并缓存jsapi_ticket（有效期7200秒）
 * 3. 使用jsapi_ticket、nonceStr、timestamp、url生成签名
 * 4. 返回配置信息给前端
 */

/**
 * 从后端API获取微信JS-SDK配置（推荐方式）
 * @param {string} url 当前页面URL
 * @returns {Promise<Object>} 微信JS-SDK配置
 */
export async function getWxConfigFromAPI(url) {
  try {
    const response = await fetch(`/api/wx/jsapi-config?url=${encodeURIComponent(url)}`)
    const result = await response.json()
    
    if (result.code === 0) {
      return result.data
    } else {
      throw new Error(`获取微信配置失败: ${result.message}`)
    }
  } catch (error) {
    console.error('从API获取微信配置出错:', error)
    throw error
  }
}

/**
 * 使用示例：
 * 
 * // 方式1：从后端API获取配置（推荐）
 * import { getWxConfigFromAPI } from './wxConfig.js'
 * import { initWxJSSDK, getCurrentUrl } from './wxJSSDK.js'
 * 
 * const config = await getWxConfigFromAPI(getCurrentUrl())
 * initWxJSSDK(config, onReady, onError)
 * 
 * // 方式2：前端直接获取（不推荐，仅用于开发测试）
 * import { getWxJSSDKConfig } from './wxConfig.js'
 * import { initWxJSSDK } from './wxJSSDK.js'
 * 
 * const config = await getWxJSSDKConfig()
 * initWxJSSDK(config, onReady, onError)
 */
