/**
 * 微信JS-SDK工具类
 * 用于处理微信JS-SDK的配置和签名算法
 * 参考文档：https://developers.weixin.qq.com/doc/subscription/guide/h5/jssdk.html
 */

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
export function generateNonceStr(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成时间戳
 * @returns {number} 当前时间戳（秒）
 */
export function generateTimestamp() {
  return Math.floor(Date.now() / 1000)
}

/**
 * 对象转URL参数字符串
 * @param {Object} obj 参数对象
 * @returns {string} URL参数字符串
 */
function objToUrlParams(obj) {
  const params = Object.keys(obj).sort().map(key => {
    return `${key}=${obj[key]}`
  }).join('&')
  return params
}

/**
 * SHA1加密实现
 * @param {string} str 待加密字符串
 * @returns {string} SHA1加密结果
 */
function sha1(str) {
  function rotateLeft(n, s) {
    return (n << s) | (n >>> (32 - s));
  }

  function cvtHex(val) {
    let str = '';
    for (let i = 7; i >= 0; i--) {
      const v = (val >>> (i * 4)) & 0x0f;
      str += v.toString(16);
    }
    return str;
  }

  function utf8Encode(str) {
    str = str.replace(/\r\n/g, '\n');
    let utftext = '';
    for (let n = 0; n < str.length; n++) {
      const c = str.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if ((c > 127) && (c < 2048)) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  }

  str = utf8Encode(str);
  const strLen = str.length;
  const wordArray = [];
  for (let i = 0; i < strLen - 3; i += 4) {
    const j = str.charCodeAt(i) << 24 | str.charCodeAt(i + 1) << 16 | str.charCodeAt(i + 2) << 8 | str.charCodeAt(i + 3);
    wordArray.push(j);
  }

  switch (strLen % 4) {
    case 0:
      wordArray.push(0x080000000);
      break;
    case 1:
      wordArray.push(str.charCodeAt(strLen - 1) << 24 | 0x0800000);
      break;
    case 2:
      wordArray.push(str.charCodeAt(strLen - 2) << 24 | str.charCodeAt(strLen - 1) << 16 | 0x08000);
      break;
    case 3:
      wordArray.push(str.charCodeAt(strLen - 3) << 24 | str.charCodeAt(strLen - 2) << 16 | str.charCodeAt(strLen - 1) << 8 | 0x80);
      break;
  }

  while ((wordArray.length % 16) !== 14) {
    wordArray.push(0);
  }

  wordArray.push(strLen >>> 29);
  wordArray.push((strLen << 3) & 0x0ffffffff);

  const w = new Array(80);
  let H0 = 0x67452301;
  let H1 = 0xEFCDAB89;
  let H2 = 0x98BADCFE;
  let H3 = 0x10325476;
  let H4 = 0xC3D2E1F0;

  for (let i = 0; i < wordArray.length; i += 16) {
    for (let j = 0; j < 16; j++) {
      w[j] = wordArray[i + j];
    }
    for (let j = 16; j <= 79; j++) {
      w[j] = rotateLeft(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);
    }

    let A = H0, B = H1, C = H2, D = H3, E = H4;

    for (let j = 0; j <= 19; j++) {
      const temp = (rotateLeft(A, 5) + ((B & C) | (~B & D)) + E + w[j] + 0x5A827999) & 0x0ffffffff;
      E = D; D = C; C = rotateLeft(B, 30); B = A; A = temp;
    }
    for (let j = 20; j <= 39; j++) {
      const temp = (rotateLeft(A, 5) + (B ^ C ^ D) + E + w[j] + 0x6ED9EBA1) & 0x0ffffffff;
      E = D; D = C; C = rotateLeft(B, 30); B = A; A = temp;
    }
    for (let j = 40; j <= 59; j++) {
      const temp = (rotateLeft(A, 5) + ((B & C) | (B & D) | (C & D)) + E + w[j] + 0x8F1BBCDC) & 0x0ffffffff;
      E = D; D = C; C = rotateLeft(B, 30); B = A; A = temp;
    }
    for (let j = 60; j <= 79; j++) {
      const temp = (rotateLeft(A, 5) + (B ^ C ^ D) + E + w[j] + 0xCA62C1D6) & 0x0ffffffff;
      E = D; D = C; C = rotateLeft(B, 30); B = A; A = temp;
    }

    H0 = (H0 + A) & 0x0ffffffff;
    H1 = (H1 + B) & 0x0ffffffff;
    H2 = (H2 + C) & 0x0ffffffff;
    H3 = (H3 + D) & 0x0ffffffff;
    H4 = (H4 + E) & 0x0ffffffff;
  }

  return (cvtHex(H0) + cvtHex(H1) + cvtHex(H2) + cvtHex(H3) + cvtHex(H4)).toLowerCase();
}

/**
 * 生成微信JS-SDK签名
 * 根据微信官方文档的签名算法实现
 * @param {Object} params 签名参数
 * @param {string} params.jsapi_ticket JS接口调用凭证
 * @param {string} params.noncestr 随机字符串
 * @param {number} params.timestamp 时间戳
 * @param {string} params.url 当前网页的URL，不包含#及其后面部分
 * @returns {string} 签名字符串
 */
export function generateSignature({ jsapi_ticket, noncestr, timestamp, url }) {
  // 参与签名的字段包括noncestr（随机字符串）, 有效的jsapi_ticket, timestamp（时间戳）, url（当前网页的URL，不包含#及其后面部分）
  const params = {
    jsapi_ticket,
    noncestr,
    timestamp,
    url
  }
  
  // 对所有待签名参数按照字段名的ASCII 码从小到大排序（字典序）后，使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串
  const string1 = objToUrlParams(params)
  
  // 对string1作sha1加密，字段名和字段值都采用原始值，不进行URL 转义
  const signature = sha1(string1)
  
  console.log('签名字符串：', string1)
  console.log('生成的签名：', signature)
  
  return signature
}

/**
 * 获取当前页面URL（不包含#及其后面部分）
 * @returns {string} 当前页面URL
 */
export function getCurrentUrl() {
  return window.location.href.split('#')[0]
}

/**
 * 检查是否在微信环境中
 * @returns {boolean} 是否在微信环境
 */
export function isWeixinBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 微信JS-SDK配置对象生成器
 * @param {Object} config 配置参数
 * @param {string} config.appId 公众号的唯一标识
 * @param {string} config.jsapi_ticket JS接口调用凭证
 * @param {string} config.url 当前网页的URL（可选，默认使用当前页面URL）
 * @returns {Object} 微信JS-SDK配置对象
 */
export function createWxConfig({ appId, jsapi_ticket, url }) {
  const nonceStr = generateNonceStr()
  const timestamp = generateTimestamp()
  const currentUrl = url || getCurrentUrl()
  
  const signature = generateSignature({
    jsapi_ticket,
    noncestr: nonceStr,
    timestamp,
    url: currentUrl
  })
  
  return {
    debug: false, // 生产环境建议设为false
    appId,
    timestamp,
    nonceStr,
    signature,
    jsApiList: [
      'scanQRCode', // 扫一扫接口
      'chooseImage', // 拍照或从手机相册中选图接口
      'previewImage', // 预览图片接口
      'uploadImage', // 上传图片接口
      'downloadImage' // 下载图片接口
    ]
  }
}

/**
 * 初始化微信JS-SDK
 * @param {Object} config 配置参数
 * @param {Function} onReady 初始化成功回调
 * @param {Function} onError 初始化失败回调
 */
export function initWxJSSDK(config, onReady, onError) {
  if (!isWeixinBrowser()) {
    console.log('不在微信环境中')
    onError && onError({ message: '不在微信环境中' })
    return
  }
  
  if (typeof wx === 'undefined') {
    console.error('微信JS-SDK未加载')
    onError && onError({ message: '微信JS-SDK未加载' })
    return
  }
  
  const wxConfig = createWxConfig(config)
  
  wx.config(wxConfig)
  
  wx.ready(function() {
    console.log('微信JS-SDK初始化成功')
    onReady && onReady()
  })
  
  wx.error(function(res) {
    console.error('微信JS-SDK初始化失败：', res)
    onError && onError(res)
  })
}
